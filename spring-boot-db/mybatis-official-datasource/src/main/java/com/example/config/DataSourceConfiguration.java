package com.example.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置类 - 基于Spring Boot官方推荐方式
 * 
 * 特点：
 * 1. 使用DataSourceProperties进行类型安全的配置绑定
 * 2. 支持HikariCP的完整配置选项
 * 3. 遵循Spring Boot自动配置最佳实践
 * 4. 支持多个数据源的独立配置
 */
@Configuration(proxyBeanMethods = false)
public class DataSourceConfiguration {

    /**
     * 主数据源配置类
     * 使用Spring Boot默认的数据源配置
     */
    @Configuration(proxyBeanMethods = false)
    @MapperScan(basePackages = "com.example.primary.mapper", 
                sqlSessionTemplateRef = "primarySqlSessionTemplate")
    static class PrimaryDataSourceConfiguration {

        /**
         * 主数据源 - 使用Spring Boot默认配置
         * 自动绑定spring.datasource.*配置
         */
        @Primary
        @Bean(name = "primaryDataSource")
        public DataSource primaryDataSource() {
            // Spring Boot会自动配置主数据源，这里只是显式声明
            return new HikariDataSource();
        }

        /**
         * 主数据源SqlSessionFactory
         */
        @Primary
        @Bean(name = "primarySqlSessionFactory")
        public SqlSessionFactory primarySqlSessionFactory(@Qualifier("primaryDataSource") DataSource dataSource) 
                throws Exception {
            SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
            sessionFactory.setDataSource(dataSource);
            
            // 设置MyBatis配置
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            configuration.setCacheEnabled(true);
            configuration.setUseGeneratedKeys(true);
            configuration.setLazyLoadingEnabled(true);
            configuration.setAggressiveLazyLoading(false);
            sessionFactory.setConfiguration(configuration);
            
            // 设置映射文件位置
            sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath:mapper/primary/*.xml"));
            
            // 设置类型别名
            sessionFactory.setTypeAliasesPackage("com.example.primary.domain");
            
            return sessionFactory.getObject();
        }

        /**
         * 主数据源SqlSessionTemplate
         */
        @Primary
        @Bean(name = "primarySqlSessionTemplate")
        public SqlSessionTemplate primarySqlSessionTemplate(
                @Qualifier("primarySqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
            return new SqlSessionTemplate(sqlSessionFactory);
        }

        /**
         * 主数据源事务管理器
         */
        @Primary
        @Bean(name = "primaryTransactionManager")
        public PlatformTransactionManager primaryTransactionManager(
                @Qualifier("primaryDataSource") DataSource dataSource) {
            return new DataSourceTransactionManager(dataSource);
        }
    }

    /**
     * 第二个数据源配置类
     * 使用官方推荐的DataSourceProperties方式
     */
    @Configuration(proxyBeanMethods = false)
    @MapperScan(basePackages = "com.example.secondary.mapper", 
                sqlSessionTemplateRef = "secondarySqlSessionTemplate")
    static class SecondaryDataSourceConfiguration {

        /**
         * 第二个数据源属性配置
         * 绑定app.datasource.*配置
         */
        @Bean(name = "secondaryDataSourceProperties")
        @ConfigurationProperties("app.datasource")
        public DataSourceProperties secondaryDataSourceProperties() {
            return new DataSourceProperties();
        }

        /**
         * 第二个数据源
         * 使用DataSourceProperties初始化，并绑定HikariCP特定配置
         */
        @Bean(name = "secondaryDataSource")
        @ConfigurationProperties("app.datasource.configuration")
        public HikariDataSource secondaryDataSource(
                @Qualifier("secondaryDataSourceProperties") DataSourceProperties properties) {
            return properties.initializeDataSourceBuilder()
                    .type(HikariDataSource.class)
                    .build();
        }

        /**
         * 第二个数据源SqlSessionFactory
         */
        @Bean(name = "secondarySqlSessionFactory")
        public SqlSessionFactory secondarySqlSessionFactory(
                @Qualifier("secondaryDataSource") DataSource dataSource) throws Exception {
            SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
            sessionFactory.setDataSource(dataSource);
            
            // 设置MyBatis配置
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            configuration.setCacheEnabled(true);
            configuration.setUseGeneratedKeys(true);
            configuration.setLazyLoadingEnabled(true);
            configuration.setAggressiveLazyLoading(false);
            sessionFactory.setConfiguration(configuration);
            
            // 设置映射文件位置
            sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath:mapper/secondary/*.xml"));
            
            // 设置类型别名
            sessionFactory.setTypeAliasesPackage("com.example.secondary.domain");
            
            return sessionFactory.getObject();
        }

        /**
         * 第二个数据源SqlSessionTemplate
         */
        @Bean(name = "secondarySqlSessionTemplate")
        public SqlSessionTemplate secondarySqlSessionTemplate(
                @Qualifier("secondarySqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
            return new SqlSessionTemplate(sqlSessionFactory);
        }

        /**
         * 第二个数据源事务管理器
         */
        @Bean(name = "secondaryTransactionManager")
        public PlatformTransactionManager secondaryTransactionManager(
                @Qualifier("secondaryDataSource") DataSource dataSource) {
            return new DataSourceTransactionManager(dataSource);
        }
    }

    /**
     * 第三个数据源配置类（可选）
     * 演示更多数据源的配置方式
     */
    @Configuration(proxyBeanMethods = false)
    @MapperScan(basePackages = "com.example.third.mapper", 
                sqlSessionTemplateRef = "thirdSqlSessionTemplate")
    static class ThirdDataSourceConfiguration {

        /**
         * 第三个数据源属性配置
         */
        @Bean(name = "thirdDataSourceProperties")
        @ConfigurationProperties("app.third-datasource")
        public DataSourceProperties thirdDataSourceProperties() {
            return new DataSourceProperties();
        }

        /**
         * 第三个数据源
         */
        @Bean(name = "thirdDataSource")
        @ConfigurationProperties("app.third-datasource.configuration")
        public HikariDataSource thirdDataSource(
                @Qualifier("thirdDataSourceProperties") DataSourceProperties properties) {
            return properties.initializeDataSourceBuilder()
                    .type(HikariDataSource.class)
                    .build();
        }

        /**
         * 第三个数据源SqlSessionFactory
         */
        @Bean(name = "thirdSqlSessionFactory")
        public SqlSessionFactory thirdSqlSessionFactory(
                @Qualifier("thirdDataSource") DataSource dataSource) throws Exception {
            SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
            sessionFactory.setDataSource(dataSource);
            
            // 设置MyBatis配置
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            configuration.setCacheEnabled(true);
            configuration.setUseGeneratedKeys(true);
            sessionFactory.setConfiguration(configuration);
            
            // 设置映射文件位置
            sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath:mapper/third/*.xml"));
            
            // 设置类型别名
            sessionFactory.setTypeAliasesPackage("com.example.third.domain");
            
            return sessionFactory.getObject();
        }

        /**
         * 第三个数据源SqlSessionTemplate
         */
        @Bean(name = "thirdSqlSessionTemplate")
        public SqlSessionTemplate thirdSqlSessionTemplate(
                @Qualifier("thirdSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
            return new SqlSessionTemplate(sqlSessionFactory);
        }

        /**
         * 第三个数据源事务管理器
         */
        @Bean(name = "thirdTransactionManager")
        public PlatformTransactionManager thirdTransactionManager(
                @Qualifier("thirdDataSource") DataSource dataSource) {
            return new DataSourceTransactionManager(dataSource);
        }
    }
}
