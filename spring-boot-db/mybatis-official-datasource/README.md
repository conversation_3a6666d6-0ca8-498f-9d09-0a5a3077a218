# MyBatis官方推荐多数据源示例

这是一个基于Spring Boot官方推荐配置方式的MyBatis多数据源示例项目，采用`DataSourceProperties`和`@ConfigurationProperties`实现类型安全的配置绑定。

## 项目特性

- ✅ 基于Spring Boot官方推荐的DataSourceProperties配置方式
- ✅ 使用@ConfigurationProperties进行类型安全的配置绑定
- ✅ 支持HikariCP连接池的完整配置选项
- ✅ 遵循Spring Boot自动配置最佳实践
- ✅ 支持多个数据源的独立配置和管理
- ✅ XML映射文件配置，支持复杂SQL
- ✅ 完整的CRUD操作示例
- ✅ RESTful API接口
- ✅ 单元测试覆盖

## 与其他方案的区别

| 特性 | mybatis-multi-datasource | mybatis-dynamic-datasource | mybatis-official-datasource |
|------|-------------------------|---------------------------|----------------------------|
| **配置方式** | 手动配置DataSource | 动态数据源路由 | **官方推荐DataSourceProperties** |
| **配置绑定** | 基础属性绑定 | 基础属性绑定 | **类型安全的@ConfigurationProperties** |
| **连接池配置** | 基础HikariCP配置 | 基础HikariCP配置 | **完整HikariCP配置选项** |
| **数据源切换** | 编译时确定（分包） | 运行时动态切换 | **编译时确定（分包）** |
| **映射方式** | XML映射文件 | 注解驱动 | **XML映射文件** |
| **适用场景** | 简单多数据源 | 读写分离 | **企业级多数据源** |

## 核心实现原理

### 1. 官方推荐配置方式

```java
@Configuration(proxyBeanMethods = false)
static class SecondaryDataSourceConfiguration {

    @Bean(name = "secondaryDataSourceProperties")
    @ConfigurationProperties("app.datasource")
    public DataSourceProperties secondaryDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "secondaryDataSource")
    @ConfigurationProperties("app.datasource.configuration")
    public HikariDataSource secondaryDataSource(DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }
}
```

### 2. 类型安全的配置绑定

```yaml
app:
  datasource:
    url: ****************************************
    username: root
    password: 123456
    configuration:
      pool-name: SecondaryHikariPool
      maximum-pool-size: 15
      minimum-idle: 3
      leak-detection-threshold: 60000
```

### 3. 完整的HikariCP配置支持

支持HikariCP的所有配置选项，包括：
- 连接池大小配置
- 连接超时配置
- 连接泄漏检测
- 连接验证配置
- 性能优化配置

## 项目结构

```
mybatis-official-datasource/
├── src/main/java/com/example/
│   ├── OfficialDatasourceApplication.java         # 启动类
│   ├── config/
│   │   └── DataSourceConfiguration.java          # 官方推荐数据源配置
│   ├── primary/                                   # 主数据源模块
│   │   ├── domain/Account.java                    # 账户实体
│   │   └── mapper/AccountMapper.java              # 账户Mapper
│   ├── secondary/                                 # 第二数据源模块
│   │   ├── domain/Transaction.java                # 交易实体
│   │   └── mapper/TransactionMapper.java          # 交易Mapper
│   └── third/                                     # 第三数据源模块
│       ├── domain/AuditLog.java                   # 审计日志实体
│       └── mapper/AuditLogMapper.java             # 审计日志Mapper
├── src/main/resources/
│   ├── application.yml                            # 配置文件
│   ├── mapper/                                    # MyBatis映射文件
│   │   ├── primary/AccountMapper.xml              # 账户映射
│   │   ├── secondary/TransactionMapper.xml        # 交易映射
│   │   └── third/AuditLogMapper.xml               # 审计日志映射
│   └── sql/                                       # 数据库脚本
└── src/test/                                      # 测试相关
```

## 数据源配置

### 主数据源 (Primary)
- **数据库**: `primary_db`
- **用途**: 账户管理
- **实体**: Account
- **配置**: Spring Boot默认数据源配置

### 第二数据源 (Secondary)
- **数据库**: `secondary_db`
- **用途**: 交易记录
- **实体**: Transaction
- **配置**: app.datasource.*

### 第三数据源 (Third)
- **数据库**: `third_db`
- **用途**: 审计日志
- **实体**: AuditLog
- **配置**: app.third-datasource.*

## 快速开始

### 1. 环境准备

确保已安装：
- JDK 17+
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库准备

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE primary_db;"
mysql -u root -p -e "CREATE DATABASE secondary_db;"
mysql -u root -p -e "CREATE DATABASE third_db;"

# 执行初始化脚本
mysql -u root -p < src/main/resources/sql/primary_db_schema.sql
mysql -u root -p < src/main/resources/sql/secondary_db_schema.sql
mysql -u root -p < src/main/resources/sql/third_db_schema.sql
```

### 3. 配置数据库连接

修改 `application.yml` 中的数据库连接信息：

```yaml
spring:
  datasource:
    url: **************************************
    username: your_username
    password: your_password

app:
  datasource:
    url: ****************************************
    username: your_username
    password: your_password
  
  third-datasource:
    url: ************************************
    username: your_username
    password: your_password
```

### 4. 运行应用

```bash
mvn spring-boot:run
```

应用启动后，访问 http://localhost:8086

### 5. 运行测试

```bash
mvn test
```

## 配置特点

### 1. 类型安全的配置绑定

使用`DataSourceProperties`确保配置的类型安全：

```java
@ConfigurationProperties("app.datasource")
public DataSourceProperties secondaryDataSourceProperties() {
    return new DataSourceProperties();
}
```

### 2. 完整的HikariCP配置

支持HikariCP的所有配置选项：

```yaml
app:
  datasource:
    configuration:
      pool-name: SecondaryHikariPool
      maximum-pool-size: 15
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      connection-init-sql: SELECT 1
      validation-timeout: 5000
```

### 3. 遵循Spring Boot最佳实践

- 使用`@Configuration(proxyBeanMethods = false)`优化性能
- 使用`@Qualifier`明确Bean依赖关系
- 使用`@Primary`标记默认数据源
- 支持Spring Boot Actuator监控

## API接口

### 账户管理 (主数据源)
- `GET /api/accounts` - 查询所有账户
- `POST /api/accounts` - 创建账户
- `PUT /api/accounts/{id}` - 更新账户
- `DELETE /api/accounts/{id}` - 删除账户

### 交易管理 (第二数据源)
- `GET /api/transactions` - 查询所有交易
- `POST /api/transactions` - 创建交易
- `PUT /api/transactions/{id}` - 更新交易

### 审计日志 (第三数据源)
- `GET /api/audit-logs` - 查询审计日志
- `POST /api/audit-logs` - 创建审计日志

## 优势特点

1. **官方推荐**: 完全遵循Spring Boot官方文档推荐的配置方式
2. **类型安全**: 使用DataSourceProperties确保配置的类型安全
3. **功能完整**: 支持HikariCP的所有配置选项
4. **易于维护**: 清晰的配置结构，易于理解和维护
5. **性能优化**: 支持连接池的高级配置和性能调优
6. **监控友好**: 集成Spring Boot Actuator，支持数据源监控

## 适用场景

- 企业级应用的多数据源配置
- 需要精细化连接池配置的场景
- 对配置类型安全有要求的项目
- 需要遵循Spring Boot最佳实践的团队

## 注意事项

1. **配置复杂度**: 相比简单配置方式，配置稍显复杂
2. **Bean管理**: 需要明确管理多个数据源相关的Bean
3. **事务管理**: 跨数据源操作需要考虑分布式事务
4. **性能调优**: 需要根据实际负载调整HikariCP参数

这个项目展示了Spring Boot官方推荐的多数据源配置方式，适合对配置质量和类型安全有较高要求的企业级应用。
